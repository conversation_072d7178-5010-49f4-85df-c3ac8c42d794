# Cloudflare Pages 헤더 설정

/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

# API 라우트에 대한 CORS 설정
/api/*
  Access-Control-Allow-Origin: *
  Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS
  Access-Control-Allow-Headers: Content-Type, Authorization

# 주유소 API 캐시 제어 (실시간 데이터를 위해 짧은 캐시)
/api/public/gas-stations*
  Cache-Control: public, max-age=300, s-maxage=300
  Vary: Accept-Encoding

# 디버그 API는 캐시하지 않음
/api/debug/*
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

# 정적 자산 캐싱
/assets/*
  Cache-Control: public, max-age=31536000, immutable

/_nuxt/*
  Cache-Control: public, max-age=31536000, immutable

# 이미지 캐싱
*.jpg
  Cache-Control: public, max-age=86400

*.png
  Cache-Control: public, max-age=86400

*.svg
  Cache-Control: public, max-age=86400

*.webp
  Cache-Control: public, max-age=86400
