<template>
  <div class="p-8 bg-red-50 min-h-screen">
    <div class="bg-red-600 text-white p-4 rounded-lg mb-6">
      <h1 class="text-3xl font-bold mb-2">🚀 CREATE 페이지 로드됨!</h1>
      <p class="text-xl">이 페이지가 보인다면 라우팅이 성공한 것입니다!</p>
    </div>

    <div class="bg-white border-2 border-red-500 rounded-lg p-6 mb-4">
      <h2 class="text-xl font-bold text-red-600 mb-4">디버깅 정보:</h2>
      <div class="space-y-2 text-sm">
        <p><strong>현재 경로:</strong> {{ $route?.path || 'Unknown' }}</p>
        <p><strong>현재 이름:</strong> {{ $route?.name || 'Unknown' }}</p>
        <p><strong>현재 시간:</strong> {{ new Date().toLocaleString() }}</p>
        <p><strong>파일 위치:</strong> pages/admin/content/festivals/create.vue</p>
        <p><strong>레이아웃:</strong> admin</p>
        <p><strong>미들웨어:</strong> auth-admin</p>
      </div>
    </div>

    <button
      @click="goBack"
      class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-lg font-semibold"
    >
      ← 목록으로 돌아가기
    </button>

    <div class="mt-6 p-4 bg-yellow-100 border border-yellow-400 rounded-lg">
      <p class="text-yellow-800">
        <strong>테스트:</strong> 이 페이지가 보인다면 create.vue 파일이 정상적으로 로드된 것입니다.
        만약 여전히 목록 페이지가 보인다면 라우팅 시스템에 문제가 있습니다.
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'admin',
  middleware: ['auth-admin']
});

function goBack() {
  console.log('goBack 함수 호출됨');
  if (import.meta.client) {
    window.location.href = '/admin/content/festivals';
  }
}
</script>

<style scoped>
/* 추가 스타일이 필요한 경우 여기에 작성 */
</style>
