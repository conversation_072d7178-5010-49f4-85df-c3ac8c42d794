<template>
  <div class="p-8">
    <h1 class="text-3xl font-bold text-green-600 mb-4">🎉 축제 등록 페이지</h1>
    <div class="bg-green-100 border border-green-400 rounded-lg p-4 mb-4">
      <p class="text-green-800 font-semibold">✅ 페이지가 성공적으로 로드되었습니다!</p>
      <p class="text-green-700 mt-2">현재 경로: {{ $route?.path || 'Unknown' }}</p>
      <p class="text-green-700">현재 시간: {{ new Date().toLocaleString() }}</p>
    </div>

    <button
      @click="goBack"
      class="px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"
    >
      ← 목록으로 돌아가기
    </button>
  </div>
</template>

<script setup lang="ts">
definePageMeta({
  layout: 'admin',
  middleware: ['auth-admin']
});

function goBack() {
  console.log('goBack 함수 호출됨');
  if (import.meta.client) {
    window.location.href = '/admin/content/festivals';
  }
}
</script>

<style scoped>
/* 추가 스타일이 필요한 경우 여기에 작성 */
</style>
