# Google AdSense 자동 광고 설정 가이드

이 프로젝트에 Google AdSense 자동 광고가 구현되었습니다.

## 🎯 자동 광고 특징

Google AdSense 자동 광고는 다음과 같은 장점을 제공합니다:
- **자동 배치**: Google AI가 최적의 위치에 광고를 자동으로 배치
- **반응형 디자인**: 모든 디바이스에서 자동으로 최적화
- **성능 최적화**: 사용자 경험을 해치지 않는 범위에서 수익 최대화
- **간편한 관리**: 별도의 광고 단위 관리 불필요

## 📋 구현 방식

### 자동 광고 스크립트
`<head>` 태그에 Google AdSense 자동 광고 스크립트가 로드됩니다:
```html
<script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-6491895061878011" crossorigin="anonymous"></script>
```

## 🔧 광고 설정

### AdSense 계정 정보
- **Publisher ID**: `ca-pub-6491895061878011`
- **상태**: 승인 완료
- **광고 유형**: 자동 광고

## 📁 파일 구조

```
public/
├── ads.txt                  # AdSense 인증 파일
└── robots.txt               # SEO 및 광고 크롤링 설정

nuxt.config.ts               # AdSense 자동 광고 스크립트 전역 로드
```

## 🚀 자동 광고 작동 방식

### 1. 스크립트 로드
`nuxt.config.ts`에서 Google AdSense 스크립트가 모든 페이지에 자동으로 로드됩니다.

### 2. 자동 배치
Google AI가 다음 요소들을 분석하여 최적의 광고 위치를 결정합니다:
- 페이지 레이아웃
- 콘텐츠 구조
- 사용자 행동 패턴
- 디바이스 유형

### 3. 광고 표시
선택된 위치에 자동으로 광고가 표시되며, 다음과 같은 특징을 가집니다:
- 반응형 디자인 자동 적용
- 페이지 로딩 성능 최적화
- 사용자 경험 고려한 배치

## 🔍 SEO 최적화

### robots.txt 설정
- Google AdSense 크롤러 허용
- 관리자 페이지 및 API 차단
- 공개 API 허용

### ads.txt 파일
Google AdSense 인증을 위한 파일이 올바르게 설정되어 있습니다.

## 📊 성능 최적화

### 자동 최적화
Google AdSense 자동 광고는 다음과 같은 성능 최적화를 자동으로 수행합니다:
- 페이지 로딩 속도에 미치는 영향 최소화
- 사용자 경험을 해치지 않는 범위에서 광고 표시
- 디바이스별 최적화된 광고 크기 및 형태 자동 선택

## 🛠️ 문제 해결

### 자동 광고가 표시되지 않는 경우
1. **AdSense 승인 상태 확인**
   - Google AdSense 계정에서 승인 상태 확인
   - 자동 광고 설정이 활성화되어 있는지 확인
   - 정책 위반 여부 점검

2. **브라우저 설정 확인**
   - 광고 차단기 비활성화
   - 개발자 도구에서 네트워크 오류 확인
   - AdSense 스크립트가 정상적으로 로드되는지 확인

3. **자동 광고 설정 확인**
   - AdSense 대시보드에서 자동 광고 설정 확인
   - 광고 유형별 활성화 상태 점검
   - 사이트별 자동 광고 설정 확인

### 개발 환경에서 테스트
```bash
# 로컬 서버 실행
npm run dev

# 브라우저에서 확인
# http://localhost:3000
```

**참고**: 자동 광고는 실제 트래픽이 있는 프로덕션 환경에서 더 잘 작동합니다.

### 배포 후 확인사항
1. ads.txt 파일 접근 가능 여부
2. robots.txt 설정 확인
3. HTTPS 연결 확인
4. 광고 정책 준수 여부
5. AdSense 대시보드에서 자동 광고 활성화 확인

## 📈 모니터링

### Google AdSense 대시보드
- 자동 광고 수익 현황 모니터링
- 광고 성능 분석 및 최적화 제안 확인
- 정책 준수 상태 확인
- 자동 광고 설정 조정

### 웹사이트 성능
- 페이지 로딩 속도 영향 최소화 (자동 최적화)
- 사용자 경험 저해 방지 (AI 기반 배치)
- 모바일 친화성 유지 (반응형 자동 조정)

## 🔒 정책 준수

### Google AdSense 정책
- 클릭 유도 금지
- 콘텐츠 품질 유지
- 트래픽 품질 관리
- 자동 광고 정책 준수

### 개인정보 보호
- 쿠키 정책 준수
- GDPR 규정 준수 (필요시)
- 사용자 동의 관리 (필요시)

## 🎯 자동 광고 최적화 팁

### AdSense 대시보드 설정
1. **자동 광고 유형 선택**
   - 페이지 내 광고: 콘텐츠 사이에 자연스럽게 배치
   - 앵커 광고: 화면 하단 고정 광고
   - 사이드 레일 광고: 데스크톱 사이드바 광고

2. **광고 밀도 조정**
   - 낮음: 사용자 경험 우선
   - 보통: 균형잡힌 설정 (권장)
   - 높음: 수익 최대화

3. **사이트별 설정**
   - 특정 페이지 제외 설정
   - 광고 유형별 활성화/비활성화
   - 모바일/데스크톱 별도 설정

## 📞 지원

문제가 발생하거나 추가 설정이 필요한 경우:
1. Google AdSense 고객센터 문의
2. 개발팀 내부 문의
3. 이 문서의 문제 해결 섹션 참조
