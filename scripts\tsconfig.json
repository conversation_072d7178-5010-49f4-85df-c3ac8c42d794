{"compilerOptions": {"target": "ES2020", "module": "ESNext", "moduleResolution": "node", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "strict": true, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "noEmit": true, "baseUrl": ".", "paths": {"~/*": ["../*"], "@/*": ["../*"]}}, "include": ["*.ts", "../server/**/*.ts"], "exclude": ["node_modules", "../.nuxt", "../dist"]}