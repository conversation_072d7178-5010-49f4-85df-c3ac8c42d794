name: Deploy to Cloudflare Pages

on:
  push:
    branches:
      - master
  workflow_dispatch:

jobs:
  deploy:
    runs-on: ubuntu-latest
    name: Deploy to Cloudflare Pages
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Clear npm cache
        run: npm cache clean --force

      - name: Install dependencies
        run: |
          npm install --legacy-peer-deps
          npm rebuild

      - name: Build with error handling
        run: |
          export NODE_OPTIONS="--max-old-space-size=4096"
          npm run build || (echo "Build failed, trying alternative approach..." && npm run build:fallback)

      - name: Deploy to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: grap
          directory: .output/public
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
